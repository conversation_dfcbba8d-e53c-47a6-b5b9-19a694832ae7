<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <el-card class="app-container">
          <template #header>
            <h1 class="header-title">Bibliographic Items Tagging Tool</h1>
            <el-text>An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB's tag pool,
              etc) (Ver 0.2)</el-text>
          </template>

          <!-- Model Selection Dropdown -->
          <!-- Floating button for advanced settings -->
          <div class="floating-button">
            <div><el-button type="primary" circle @click="drawerVisible = true" :icon="Setting" /></div>
          </div>

          <SettingsDrawer
            v-model:drawerVisible="drawerVisible"
            :screenIsPortrait="screenIsPortrait"
            v-model:selectedModel="selectedModel"
            :modelOptions="modelOptions"
            v-model:apiUrl="apiUrl"
            v-model:apiAllTagsUrl="apiAllTagsUrl"
          />

          <DataImporter
            @itemsUpdated="handleItemsUpdated"
          />
          <ProcessingControls
            :biblioItems="biblioItems"
            :isCSVLoading="isCSVLoading"
            :isLoading="isLoading"
            :elapsedCSVTime="elapsedCSVTime"
            :elapsedTime="elapsedTime"
            v-model:batchSize="batchSize"
            :suggestedBatchSize="suggestedBatchSize"
            @submitBiblioItems="submitBiblioItems"
            @clearAllItems="clearAllItems"
          />

          <ResultsDisplay
            :results="results"
            :allIndexedBiblioItems="allIndexedBiblioItems"
            :screenIsPortrait="screenIsPortrait"
            :tagNames="tagNames"
            :allTagCandidates="allTagCandidates"
            :apiAllTagsUrl="apiAllTagsUrl"
            :isFetchingTags="isFetchingTags"
            :hasLoadedTags="hasLoadedTags"
            @tagsUpdated="handleTagsUpdated"
          />

          <ExportControls
            :results="results"
            :allIndexedBiblioItems="allIndexedBiblioItems"
            :allTagCandidates="allTagCandidates"
            :deselectedTags="deselectedTags"
            :newTags="newTags"
            :currentSource="currentSource"
            :zoteroConfig="zoteroConfig"
            :zoteroBaseUrl="zoteroBaseUrl"
            :screenIsPortrait="screenIsPortrait"
            :apiAllTagsUrl="apiAllTagsUrl"
            :isFetchingTags="isFetchingTags"
            :hasLoadedTags="hasLoadedTags"
            @fetchAllTags="fetchAllTags"
          />
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, toRaw, isReactive } from 'vue'
import axios from 'axios'
import { debounce } from 'lodash'
import {
  ElContainer,
  ElMain,
  ElCard,
  ElButton,
  ElText,
  ElMessage,
  ElMessageBox
} from 'element-plus'
import { ZoomIn, ZoomOut, Setting } from '@element-plus/icons-vue'

// Import new components
import SettingsDrawer from './components/SettingsDrawer.vue'
import DataImporter from './components/DataImporter.vue'
import ProcessingControls from './components/ProcessingControls.vue'
import ResultsDisplay from './components/ResultsDisplay.vue'
import ExportControls from './components/ExportControls.vue'

const drawerVisible = ref(false)
const screenIsPortrait = ref(window.innerHeight > window.innerWidth);
const drawerDirection = computed(() => {
  return screenIsPortrait.value ? 'btt' : 'rtl'; // 'btt' = bottom, 'rtl' = right
});

const biblioItems = ref([]) // This is for storing the original items imported from data sources
const allIndexedBiblioItems = ref([]) // This is the indexed version of biblioItems. It will be populated when calling submitBiblioItems function.
const results = ref([]) // This is for storing the results from the API.
const isLoading = ref(false)

// Tag state for export functionality (received from ResultsDisplay)
const deselectedTags = ref(new Set())
const newTags = ref(new Map())

const apiUrl = ref('http://127.0.0.1:5011/api/generate_tags')
const apiAllTagsUrl = ref('http://127.0.0.1:5011/api/tags/all')
const batchSize = ref(null)
const suggestedBatchSize = computed(() => {
  const length = biblioItems.value.length;
  if (length === 0) {
    return 1; // Default when there are no items
  } else if (length <= 5) {
    return length; // Process all items if there are 5 or fewer
  } else if (length <= 50) {
    return Math.ceil(length / 5); // Split into ~5 batches for datasets between 6 and 50
  } else if (length <= 200) {
    return Math.ceil(length / 10); // Split into ~10 batches for datasets between 51 and 200
  } else {
    return Math.ceil(length / 20); // Split into ~20 batches for datasets larger than 200
  }
});


const isCSVLoading = ref(false)
const elapsedTime = ref(0)
const elapsedCSVTime = ref(0)
let timerInterval = null
let csvTimerInterval = null

// Add model selection related refs
const selectedModel = ref('gpt-4o-mini')
const modelOptions = ref([
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
  { value: 'pytextrank', label: 'pytextrank (very fast)' }
  // Add more model options here
])

// TAG CONFIGURATION
const allTagCandidates = ref([]) // Store all tags from the curated tag pool
const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name))
const isFetchingTags = ref(false)
const hasLoadedTags = ref(false) // Track if tags have been loaded


// Function to update screen orientation
const updateScreenOrientation = () => {
  screenIsPortrait.value = window.innerHeight > window.innerWidth;
};

// Add event listener to handle screen resizing
onMounted(() => {
  window.addEventListener('resize', updateScreenOrientation);
});

// Clean up the event listener when the component is unmounted
onUnmounted(() => {
  window.removeEventListener('resize', updateScreenOrientation);
});

// Data source tracking (simplified)
const currentSource = ref('') // To track data source

// Zotero configuration (received from DataImporter when needed)
const zoteroConfig = ref(null)
const zoteroBaseUrl = ref('')

// Handle items updated from DataImporter
const handleItemsUpdated = (items, removedItems, source, zoteroData = null) => {
  biblioItems.value = items
  currentSource.value = source

  // Store Zotero configuration if provided
  if (source === 'Zotero' && zoteroData) {
    zoteroConfig.value = zoteroData.config
    zoteroBaseUrl.value = zoteroData.baseUrl
  } else if (source !== 'Zotero') {
    // Clear Zotero config when switching to non-Zotero sources
    zoteroConfig.value = null
    zoteroBaseUrl.value = ''
  }

  // Clear any existing results when new items are loaded
  results.value = []
  allIndexedBiblioItems.value = []
}

// Handle tags updated from ResultsDisplay
const handleTagsUpdated = (tagData) => {
  deselectedTags.value = tagData.deselectedTags
  newTags.value = tagData.newTags
}



const startTimer = (isCSV = false) => {
  if (isCSV) {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  } else {
    elapsedTime.value = 0
    timerInterval = setInterval(() => {
      elapsedTime.value++
    }, 1000)
  }
}

const stopTimer = (isCSV = false) => {
  if (isCSV) {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  } else {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
  }
}

const submitBiblioItems = async () => {
  try {
    isCSVLoading.value = true
    startTimer(true)

    // Clear previous results
    results.value = []

    // VERY IMPORTANT: Index all items. After this step, allIndexedBiblioItems should be used instead of biblioItems.
    allIndexedBiblioItems.value = biblioItems.value.map((item, idx) => ({
      ...item,
      index: idx,
    }))

    // Use the user's chosen batch size (or suggested if not set)
    const size = batchSize.value || suggestedBatchSize.value

    // Process items in batches
    for (let i = 0; i < allIndexedBiblioItems.value.length; i += size) {
      const batch = allIndexedBiblioItems.value.slice(i, i + size)
      const response = await axios.post(`${apiUrl.value}`, {
        model: selectedModel.value,
        items: batch.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      results.value.push(...response.data)
      ElMessage.success(`Batch ${Math.floor(i / size) + 1} tagged successfully!`)
      await fetchAllTags()
    }
  } catch (error) {
    console.error('Error batch-tagging articles:', error)
    ElMessage.error('An error occurred while batch-tagging articles.')
  } finally {
    stopTimer(true)
    isCSVLoading.value = false
  }
}

const clearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    biblioItems.value = []
    allIndexedBiblioItems.value = []
    results.value = []
    currentSource.value = ''
    batchSize.value = null
    deselectedTags.value = new Set()
    newTags.value = new Map()
    // allTagCandidates.value = []
    ElMessage({
      type: 'success',
      message: 'All items have been cleared',
      duration: 2000
    })
  }).catch((error) => {
    console.error('Error in clearAllItems:', error)
  })
}










// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}













// Helper functions for components
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal; /* Allow text to wrap */
  word-break: break-word; /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%; /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%; /* Ensure the table takes full width of its container */
  max-width: 100%; /* Prevent overflow */
  table-layout: fixed; /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word; /* Break long words to prevent overflow */
  white-space: normal; /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>